import 'package:echipta/assets/app_assets.dart';
import 'package:echipta/assets/app_colors.dart';
import 'package:echipta/core/utils/extentions.dart';
import 'package:echipta/features/common/widgets/w_button.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';

class WErrorModalPopup extends StatelessWidget {
  const WErrorModalPopup({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(20),
      child: SizedBox(
        width: double.infinity,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Image.asset(
              AppAssets.error,
              width: MediaQuery.of(context).size.width * 0.5,
            ),
            const Gap(20),
            Text(
              "To'lov qilishda xatolik yuz berdi :(",
              style: context.textTheme.displayLarge,
            ),
            const Gap(10),
            Text(
              "Iltimos hisobingizni tekshiring yoki qaytadan harakat qilib ko'ring",
              textAlign: TextAlign.center,
              style: context.textTheme.bodySmall!.copyWith(
                color: AppColors.darkGrey,
              ),
            ),
            const Gap(20),
            WButton(onTap: () {}, txt: "Qayta urinish"),
            Gap(context.padding.bottom),
          ],
        ),
      ),
    );
  }
}
