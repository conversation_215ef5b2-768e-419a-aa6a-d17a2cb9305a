import 'package:dio/dio.dart';
import 'package:echipta/core/api/dio_settings.dart';
import 'package:echipta/core/exceptions/custom_exception.dart';
import 'package:echipta/core/exceptions/exceptions.dart';
import 'package:echipta/core/singleton/singleton.dart';
import 'package:echipta/core/storage/storage_repository.dart';
import 'package:echipta/core/storage/store_keys.dart';
import 'package:echipta/core/utils/params.dart';
import 'package:echipta/features/common/models/error_model.dart';
import 'package:echipta/features/home/<USER>/models/sector_model.dart';
import 'package:echipta/features/home/<USER>/models/ticket_model.dart';
import 'package:echipta/features/order/data/models/order_history_model.dart';

abstract class OrderDatasource {
  Future<Map<String, dynamic>> ticketPayment(OrderParams params);
  Future<SectorModel> getSectorData(SectorParams params);
  Future<TicketModel> getTicketInfo(OrderParams params);
  Future<List<OrderHistoryModel>> getOrderHistory();
  Future<dynamic> orderIdCard(OrderParams params);
  Future<int> getOrderStatus(IdParam params);
}

class OrderDatasourceImpl implements OrderDatasource {
  final Dio _dio = serviceLocator<DioSettings>().dio;

  /// Helper method to handle different error response formats
  void _handleErrorResponse(Map<String, dynamic> responseData, int? statusCode) {
    // Handle the specific error format: {"code":422,"message":"...","data":{"field":["error"]}}
    if (responseData.containsKey('code') && responseData.containsKey('message')) {
      String errorMessage = responseData['message'] ?? 'Unknown error';
      String errorKey = 'Unknown error';

      // Extract field-specific error if available
      if (responseData.containsKey('data') && responseData['data'] is Map<String, dynamic>) {
        final dataErrors = responseData['data'] as Map<String, dynamic>;
        if (dataErrors.isNotEmpty) {
          final firstField = dataErrors.keys.first;
          final fieldErrors = dataErrors[firstField];
          if (fieldErrors is List && fieldErrors.isNotEmpty) {
            errorMessage = fieldErrors.first.toString();
            errorKey = firstField;
          }
        }
      }

      throw ServerException(
        statusCode: responseData['code'] ?? statusCode ?? 500,
        errorMessage: errorMessage,
        errorKey: errorKey,
      );
    }

    // Fallback to original ErrorModel parsing for other error formats
    try {
      final error = ErrorModel.fromJson(responseData);
      if (error.errors.isNotEmpty) {
        throw ServerException(
          statusCode: statusCode ?? 500,
          errorMessage: error.errors.first.message,
          errorKey: error.errors.first.error,
        );
      } else {
        throw ServerException(
          statusCode: statusCode ?? 500,
          errorMessage: responseData.toString(),
          errorKey: "Unknown error",
        );
      }
    } catch (e) {
      // If ErrorModel parsing fails, use the raw response
      throw ServerException(
        statusCode: statusCode ?? 500,
        errorMessage: responseData['message']?.toString() ?? responseData.toString(),
        errorKey: "Parse error",
      );
    }
  }
  @override
  Future<Map<String, dynamic>> ticketPayment(OrderParams params) async {
    try {
      final token = StorageRepository.getString(StoreKeys.token);
      Map<String, dynamic> data = {};
      data.addAll({
        "match_id": params.matchId,
        "sector": params.sector,
        "row": params.row,
        "seat": params.seat,
        "payment_type": params.paymentType,
      });
      if (params.userForGift != null && params.userForGift!.isNotEmpty) {
        data.addAll({"gift_to_user": params.userForGift});
      }
      final response = await _dio.post(
        "/games/ticket-payment",
        data: data,
        options: Options(headers: {"Authorization": token}),
      );
      if (response.statusCode! >= 200 && response.statusCode! < 300) {
        // TODO: buyerni optimallashtirish kerak bo'ladi
        if (response.data["message"] == "Sizda ushbu o'yin uchun faol chipta bor!" ||
            response.data["message"] == "Do'stingizda ushbu o'yin uchun faol chipta bor!") {
          return {"error": true, "message": response.data["message"]};
        } else if (response.data["message"] == "Balance not enough") {
          return {"error": true, "message": response.data["message"]};
        }
        else if (response.data["message"] == "successfully payed") {
          return {"error": false, "message": "Muvaffaqiyatli to'landi"};
        }
        else {
          // Success case with payment data
          final data = response.data["data"];
          if (data != null) {
            return {"error": false, "data": data};
          } else {
            return {"error": true, "message": "No data received"};
          }
        }
      } else {
        if (response.data is Map<String, dynamic>) {
          // Handle specific error messages from server (like 440 status code)
          if (response.data["message"] == "Do'stingizda ushbu o'yin uchun faol chipta bor!" ||
              response.data["message"] == "Sizda ushbu o'yin uchun faol chipta bor!") {
            return {"error": true, "message": response.data["message"]};
          } else if (response.data["message"] == "Balance not enough") {
            return {"error": true, "message": response.data["message"]};
          }

          _handleErrorResponse(response.data, response.statusCode);
        } else {
          throw ServerException(
            statusCode: response.statusCode ?? 500,
            errorMessage: response.data.toString(),
            errorKey: "Unknown error",
          );
        }
      }

      // This should never be reached due to _handleErrorResponse always throwing
      throw ServerException(
        statusCode: 500,
        errorMessage: "Unexpected error occurred",
        errorKey: "Unknown error",
      );
    } on DioException catch (error) {
      throw DioException(requestOptions: error.requestOptions);
    } on CustomException {
      rethrow;
    } on ServerException {
      rethrow;
    } on Exception catch (error) {
      throw CustomException(message: '$error', code: '141');
    }
  }

  @override
  Future<SectorModel> getSectorData(SectorParams params) async {
    try {
      final response = await _dio.get(
        "/games/get-sector-info?sector=${params.sector}&match_id=${params.match_id}",
      );
      if (response.statusCode! >= 200 && response.statusCode! < 300) {
        return SectorModel.fromJson(response.data["data"]);
      } else {
        if (response.data is Map<String, dynamic>) {
          _handleErrorResponse(response.data, response.statusCode);
        } else {
          throw ServerException(
            statusCode: response.statusCode ?? 500,
            errorMessage: response.data.toString(),
            errorKey: "Unknown error",
          );
        }
      }

      // This should never be reached due to _handleErrorResponse always throwing
      throw ServerException(
        statusCode: 500,
        errorMessage: "Unexpected error occurred",
        errorKey: "Unknown error",
      );

    } on DioException catch (error) {
      throw DioException(requestOptions: error.requestOptions);
    } on CustomException {
      rethrow;
    } on ServerException {
      rethrow;
    } on Exception catch (error) {
      throw CustomException(message: '$error', code: '141');
    }
  }

  @override
  Future<TicketModel> getTicketInfo(OrderParams params) async {
    try {
      final data = {
        "match_id": params.matchId,
        "sector": params.sector,
        "row": params.row,
        "seat": params.seat,
      };
      final response = await _dio.post("/games/get-ticket-info/", data: data);
      if (response.statusCode! >= 200 && response.statusCode! < 300) {
        return TicketModel.fromJson(response.data["data"]);
      } else {
        if (response.data is Map<String, dynamic>) {
          _handleErrorResponse(response.data, response.statusCode);
        } else {
          throw ServerException(
            statusCode: response.statusCode ?? 500,
            errorMessage: response.data.toString(),
            errorKey: "Unknown error",
          );
        }
      }

      // This should never be reached due to _handleErrorResponse always throwing
      throw ServerException(
        statusCode: 500,
        errorMessage: "Unexpected error occurred",
        errorKey: "Unknown error",
      );
    } on DioException catch (error) {
      throw DioException(requestOptions: error.requestOptions);
    } on CustomException {
      rethrow;
    } on ServerException {
      rethrow;
    } on Exception catch (error) {
      throw CustomException(message: '$error', code: '141');
    }
  }

  @override
  Future<List<OrderHistoryModel>> getOrderHistory() async {
    try {
      final token = StorageRepository.getString(StoreKeys.token);

      final response = await _dio.get(
        "/clients/my-orders",
        options: Options(headers: {"Authorization": token}),
      );
      if (response.statusCode! >= 200 && response.statusCode! < 300) {
        return (response.data["data"]["items"] as List)
            .map((e) => OrderHistoryModel.fromJson(e as Map<String, dynamic>))
            .toList();
      } else {
        if (response.data is Map<String, dynamic>) {
          _handleErrorResponse(response.data, response.statusCode);
        } else {
          throw ServerException(
            statusCode: response.statusCode ?? 500,
            errorMessage: response.data.toString(),
            errorKey: "Unknown error",
          );
        }
      }

      // This should never be reached due to _handleErrorResponse always throwing
      throw ServerException(
        statusCode: 500,
        errorMessage: "Unexpected error occurred",
        errorKey: "Unknown error",
      );
    } on DioException catch (error) {
      throw DioException(requestOptions: error.requestOptions);
    } on CustomException {
      rethrow;
    } on ServerException {
      rethrow;
    } on Exception catch (error) {
      throw CustomException(message: '$error', code: '141');
    }
  }

  @override
  Future<dynamic> orderIdCard(OrderParams params) async {
    try {
      final token = StorageRepository.getString(StoreKeys.token);
      final data = {
        "type": params.type, //seasoncard,idcard
        "match_id": params.matchId,
        "sector": params.sector,
        "row": params.row,
        "seat": params.seat,
        "city": params.city,
        "payment_type": params.paymentType,
      };
      final response = await _dio.post(
        "/clients/cards/request-card",
        data: data,
        options: Options(headers: {"Authorization": token}),
      );
      if (response.statusCode! >= 200 && response.statusCode! < 300) {
        if (response.data["data"].isEmpty) {
          return null;
        } else if (response.data["message"] == "Balance not enough") {
          // Return error response structure to distinguish from success
          return {"error": true, "message": response.data["message"]};
        } else {
          // Return success response structure
          return {"error": false, "payment_url": response.data["data"]["payment_url"]};
        }
      } else {
        if (response.data is Map<String, dynamic>) {
          _handleErrorResponse(response.data, response.statusCode);
        } else {
          throw ServerException(
            statusCode: response.statusCode ?? 500,
            errorMessage: response.data.toString(),
            errorKey: "Unknown error",
          );
        }
      }
    } on DioException catch (error) {
      throw DioException(requestOptions: error.requestOptions);
    } on CustomException {
      rethrow;
    } on ServerException {
      rethrow;
    } on Exception catch (error) {
      throw CustomException(message: '$error', code: '141');
    }
  }

  @override
  Future<int> getOrderStatus(IdParam params) async {
    try {
      final token = StorageRepository.getString(StoreKeys.token);

      final response = await _dio.get(
        "/games/order-status?order_id=${params.id}",
        options: Options(headers: {"Authorization": token}),
      );
      if (response.statusCode! >= 200 && response.statusCode! < 300) {
        return response.data["data"]["status"];
      } else {
        if (response.data is Map<String, dynamic>) {
          _handleErrorResponse(response.data, response.statusCode);
        } else {
          throw ServerException(
            statusCode: response.statusCode ?? 500,
            errorMessage: response.data.toString(),
            errorKey: "Unknown error",
          );
        }
      }

      // This should never be reached due to _handleErrorResponse always throwing
      throw ServerException(
        statusCode: 500,
        errorMessage: "Unexpected error occurred",
        errorKey: "Unknown error",
      );
    } on DioException catch (error) {
      throw DioException(requestOptions: error.requestOptions);
    } on CustomException {
      rethrow;
    } on ServerException {
      rethrow;
    } on Exception catch (error) {
      throw CustomException(message: '$error', code: '141');
    }
  }
}
