import 'package:echipta/assets/app_assets.dart';
import 'package:echipta/assets/app_colors.dart';
import 'package:echipta/core/utils/extentions.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:google_fonts/google_fonts.dart';

class WErrorModal extends StatelessWidget {
  const WErrorModal({super.key, required this.title});
  final String title;
  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: double.maxFinite,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            padding: EdgeInsets.fromLTRB(
              20,
              20,
              20,
              20 + context.padding.bottom,
            ),
            decoration: BoxDecoration(
              color: AppColors.white,
              borderRadius: BorderRadius.vertical(top: Radius.circular(30)),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Image.asset(
                  title ==
                          "Sizda mazkur bahs uchun faol chipta mavjudligi sababli chipta sotib ololmaysiz"
                      ? AppAssets.wait
                      : AppAssets.error,
                  width: 80,
                  height: 80,
                ),
                Gap(20),
                Text(
                  title ==
                          "Sizda mazkur bahs uchun faol chipta mavjudligi sababli chipta sotib ololmaysiz"
                      ? "Ogohlantirish!"
                      : "Xatolik yuz berdi!",
                  style: TextStyle(
                    fontFamily: GoogleFonts.raleway().fontFamily,
                    fontWeight: FontWeight.w700,
                    fontSize: 19,
                  ),
                ),
                Gap(5),
                Text(
                  title,
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    fontFamily: GoogleFonts.raleway().fontFamily,
                    fontWeight: FontWeight.w600,
                    fontSize: 13,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
